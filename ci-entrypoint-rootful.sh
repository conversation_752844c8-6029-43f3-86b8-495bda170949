#!/usr/bin/env bash
set -euo pipefail

# 1) 启动 containerd（root）
containerd &

# 2) 等待 socket 就绪
until [ -S /run/containerd/containerd.sock ]; do sleep 0.2; done

# 3) 启动 buildkitd，使用 containerd worker
mkdir -p /run/buildkit
buildkitd --oci-worker=false \
          --containerd-worker=true \
          --addr unix:///run/buildkit/buildkitd.sock &

# 4) 等待 buildkitd socket 就绪
until [ -S /run/buildkit/buildkitd.sock ]; do sleep 0.2; done

# 5) 交给原业务 CMD
exec "$@"
