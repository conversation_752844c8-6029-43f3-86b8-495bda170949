# =============================================================================
# LiteOps CI/CD Platform - 超级优化的Multi-stage Dockerfile
# =============================================================================
# 第一阶段：构建和工具安装阶段
FROM debian:bullseye-slim AS builder

# 设置构建时的环境变量
ENV DEBIAN_FRONTEND=noninteractive \
    PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    # Java环境变量
    JAVA_HOME=/usr/local/java/jdk1.8.0_211 \
    MAVEN_HOME=/usr/local/maven/apache-maven-3.8.8 \
    # NVM环境变量
    NVM_DIR=/root/.nvm \
    # 容器工具版本
    NERDCTL_VERSION=2.0.0 \
    BUILDKIT_VERSION=0.13.0 \
    CONTAINERD_VERSION=1.7.15 \
    RUNC_VERSION=1.1.12

# =============================================================================
# 系统基础配置和轻量化软件安装
# =============================================================================
RUN set -eux; \
    # 配置阿里云镜像源以加速下载
    sed -i 's/deb.debian.org/mirrors.aliyun.com/g' /etc/apt/sources.list && \
    sed -i 's/security.debian.org/mirrors.aliyun.com/g' /etc/apt/sources.list && \
    # 只安装最必要的包，移除开发工具
    apt-get update && \
    apt-get install -y --no-install-recommends \
        # Python 最小运行时
        python3.9 \
        python3-pip \
        # 最小系统工具
        curl \
        ca-certificates \
        # SSH（部署必需）
        openssh-client \
        # Git（GitPython依赖）
        git \
        # 进程管理
        procps \
        bash \
        && \
    # 创建Python符号链接
    ln -sf /usr/bin/python3.9 /usr/bin/python3 && \
    ln -sf /usr/bin/python3.9 /usr/bin/python && \
    # 配置pip镜像源
    pip config set global.index-url https://mirrors.aliyun.com/pypi/simple/ && \
    pip config set install.trusted-host mirrors.aliyun.com && \
    # SSH客户端配置
    mkdir -p /root/.ssh && \
    chmod 700 /root/.ssh && \
    echo "StrictHostKeyChecking no" > /root/.ssh/config && \
    echo "UserKnownHostsFile /dev/null" >> /root/.ssh/config && \
    echo "LogLevel ERROR" >> /root/.ssh/config && \
    chmod 600 /root/.ssh/config && \
    # 轻量化安装NVM（只安装核心文件）
    curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.3/install.sh | bash && \
    echo 'export NVM_DIR="$HOME/.nvm"' >> /root/.bashrc && \
    echo '[ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"' >> /root/.bashrc && \
    echo '[ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh" --no-use' >> /root/.profile && \
    # 创建Java和Maven安装目录
    mkdir -p /usr/local/java /usr/local/maven && \
    # 安装精简的容器运行时工具（只安装必需的）
    curl -L https://github.com/containerd/containerd/releases/download/v${CONTAINERD_VERSION}/containerd-${CONTAINERD_VERSION}-linux-amd64.tar.gz \
      | tar -xz -C /usr/local/bin --strip-components=1 bin/containerd bin/ctr && \
    curl -L https://github.com/opencontainers/runc/releases/download/v${RUNC_VERSION}/runc.amd64 \
      -o /usr/local/sbin/runc && chmod +x /usr/local/sbin/runc && \
    curl -L https://github.com/moby/buildkit/releases/download/v${BUILDKIT_VERSION}/buildkit-v${BUILDKIT_VERSION}.linux-amd64.tar.gz \
      | tar -xz -C /usr/local bin/buildctl bin/buildkitd && \
    curl -L https://github.com/containerd/nerdctl/releases/download/v${NERDCTL_VERSION}/nerdctl-${NERDCTL_VERSION}-linux-amd64.tar.gz \
      | tar -xz -C /usr/local/bin && \
    ln -s /usr/local/bin/nerdctl /usr/local/bin/docker && \
    # 彻底清理APT缓存和临时文件
    apt-get autoremove -y && \
    apt-get autoclean && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/* /tmp/* /var/tmp/* /var/cache/apt/* /root/.cache/*

# =============================================================================
# 精简Java环境安装
# =============================================================================
COPY jdk-8u211-linux-x64.tar.gz apache-maven-3.8.8-bin.tar.gz /tmp/

RUN set -eux; \
    # 解压JDK和Maven
    tar -xzf /tmp/jdk-8u211-linux-x64.tar.gz -C /usr/local/java && \
    tar -xzf /tmp/apache-maven-3.8.8-bin.tar.gz -C /usr/local/maven && \
    # 立即清理压缩包
    rm -f /tmp/jdk-8u211-linux-x64.tar.gz /tmp/apache-maven-3.8.8-bin.tar.gz && \
    # 极度精简JDK - 删除所有不必要的文件
    cd /usr/local/java/jdk1.8.0_211 && \
    rm -rf src.zip javafx-src.zip man sample demo \
           COPYRIGHT LICENSE README.html THIRDPARTYLICENSEREADME.txt \
           release ASSEMBLY_EXCEPTION && \
    # 删除不常用的JDK工具（保留核心编译和运行工具）
    cd bin && \
    rm -f appletviewer extcheck jarsigner java-rmi.cgi \
          javadoc javah javap javaws jcmd jconsole jdb jhat \
          jinfo jmap jps jrunscript jsadebugd jstack jstat \
          jstatd jvisualvm native2ascii orbd policytool \
          rmic rmid rmiregistry schemagen serialver servertool \
          tnameserv wsgen wsimport xjc && \
    # 删除JRE中的不必要文件
    cd ../jre && \
    rm -rf COPYRIGHT LICENSE README THIRDPARTYLICENSEREADME.txt \
           ASSEMBLY_EXCEPTION release && \
    cd bin && \
    rm -f javaws jvisualvm orbd policytool rmid \
          rmiregistry servertool tnameserv && \
    # 精简Maven安装，删除文档和示例
    cd /usr/local/maven/apache-maven-3.8.8 && \
    rm -rf LICENSE NOTICE README.txt

# =============================================================================
# 第二阶段：超轻量运行时镜像
# =============================================================================
FROM debian:bullseye-slim

# 设置运行时环境变量
ENV DEBIAN_FRONTEND=noninteractive \
    PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    # Java环境变量
    JAVA_HOME=/usr/local/java/jdk1.8.0_211 \
    MAVEN_HOME=/usr/local/maven/apache-maven-3.8.8 \
    # NVM环境变量
    NVM_DIR=/root/.nvm \
    # 更新PATH环境变量
    PATH=/usr/local/java/jdk1.8.0_211/bin:/usr/local/maven/apache-maven-3.8.8/bin:/usr/local/bin:/usr/local/sbin:$PATH

# =============================================================================
# 运行时最小化系统配置
# =============================================================================
RUN set -eux; \
    # 配置阿里云镜像源
    sed -i 's/deb.debian.org/mirrors.aliyun.com/g' /etc/apt/sources.list && \
    sed -i 's/security.debian.org/mirrors.aliyun.com/g' /etc/apt/sources.list && \
    # 只安装绝对必需的运行时包
    apt-get update && \
    apt-get install -y --no-install-recommends \
        # Python运行时（最小）
        python3.9 \
        python3-pip \
        # 最小网络工具
        curl \
        ca-certificates \
        # SSH（部署必需）
        openssh-client \
        # Git（GitPython依赖）
        git \
        # 轻量web服务器
        nginx-light \
        # 进程管理
        procps \
        bash \
        && \
    # 创建Python符号链接
    ln -sf /usr/bin/python3.9 /usr/bin/python3 && \
    ln -sf /usr/bin/python3.9 /usr/bin/python && \
    # 配置pip镜像源
    pip config set global.index-url https://mirrors.aliyun.com/pypi/simple/ && \
    pip config set install.trusted-host mirrors.aliyun.com && \
    # 创建必要的目录
    mkdir -p /app/logs && \
    # 移除nginx不必要的模块和文件
    rm -rf /var/log/nginx/* /var/lib/nginx/body /var/lib/nginx/fastcgi \
           /var/lib/nginx/proxy /var/lib/nginx/scgi /var/lib/nginx/uwsgi \
           /etc/nginx/sites-enabled/default && \
    # 彻底清理包缓存和临时文件
    apt-get autoremove -y && \
    apt-get autoclean && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/* /tmp/* /var/tmp/* /var/cache/apt/* /root/.cache/* \
           /var/cache/debconf/* /var/lib/dpkg/info/* /usr/share/doc/* \
           /usr/share/man/* /usr/share/locale/* /usr/share/info/*

# =============================================================================
# 从构建阶段复制精简的文件
# =============================================================================
# 复制SSH配置
COPY --from=builder /root/.ssh /root/.ssh

# 复制精简的NVM环境
COPY --from=builder /root/.nvm /root/.nvm
COPY --from=builder /root/.bashrc /root/.bashrc
COPY --from=builder /root/.profile /root/.profile

# 复制精简后的Java环境
COPY --from=builder /usr/local/java /usr/local/java
COPY --from=builder /usr/local/maven /usr/local/maven

# 只复制关键的容器工具
COPY --from=builder /usr/local/bin/containerd /usr/local/bin/ctr /usr/local/bin/nerdctl /usr/local/bin/buildctl /usr/local/bin/buildkitd /usr/local/bin/
COPY --from=builder /usr/local/sbin/runc /usr/local/sbin/
# 创建docker符号链接
RUN ln -s /usr/local/bin/nerdctl /usr/local/bin/docker

# =============================================================================
# 应用程序配置
# =============================================================================
# 设置工作目录
WORKDIR /app

# 配置Nginx - 复制自定义配置文件
COPY web/nginx.conf /etc/nginx/sites-available/default
RUN ln -sf /etc/nginx/sites-available/default /etc/nginx/sites-enabled/default

# 复制前端构建文件到Nginx静态文件目录
COPY web/dist/ /usr/share/nginx/html/

# 优化Python依赖安装
COPY backend/requirements.txt /app/
RUN pip install --no-cache-dir -r requirements.txt && \
    # 清理pip缓存和不必要的文件
    rm -rf /root/.cache/pip /tmp/* && \
    # 移除pip的缓存目录
    pip cache purge 2>/dev/null || true

# 复制后端应用代码
COPY backend/ /app/

# 复制启动脚本并设置执行权限
COPY docker-entrypoint.sh /app/
COPY ci-entrypoint-rootful.sh /usr/local/bin/
RUN chmod +x /app/docker-entrypoint.sh /usr/local/bin/ci-entrypoint-rootful.sh

# =============================================================================
# 容器配置
# =============================================================================
# 暴露端口
# 80: Nginx Web服务器端口
# 8900: Django后端API端口
EXPOSE 80 8900

# 设置容器入口点和默认命令
ENTRYPOINT ["/usr/local/bin/ci-entrypoint-rootful.sh"]
CMD ["/app/docker-entrypoint.sh"]